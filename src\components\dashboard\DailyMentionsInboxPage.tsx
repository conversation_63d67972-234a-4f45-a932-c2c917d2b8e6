import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UniversalFilter, FilterOption, FilterValues } from '@/components/ui/UniversalFilter';
import { Inbox, Mail, MailOpen, Calendar, User, Building, TrendingUp, Eye, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';

// Mock data for daily mentions
const mockDailyMentions = [
  {
    id: '1',
    date: '2024-01-15',
    title: 'Stanbic IBTC Promotes Financial Inclusion With Digital Solutions',
    content: 'Stanbic IBTC Bank, a subsidiary of Stanbic IBTC Holdings Plc, has restated its commitment to enhancing financial inclusion through innovative digital solutions...',
    sentiment: 'Positive',
    reporter: '<PERSON>',
    publication: 'BusinessDay',
    source: 'Online',
    category: 'SUBSIDIARIES',
    isRead: false,
    links: ['https://businessday.ng/news/article/stanbic-ibtc-promotes-financial-inclusion'],
    analystName: '<PERSON>',
    submittedAt: '2024-01-15T09:30:00Z'
  },
  {
    id: '2',
    date: '2024-01-15',
    title: 'Banking Sector Shows Resilience Amid Economic Challenges',
    content: 'The Nigerian banking sector continues to demonstrate remarkable resilience despite ongoing economic challenges, with major players like Stanbic IBTC leading the charge...',
    sentiment: 'Positive',
    reporter: 'Michael Chen',
    publication: 'The Guardian',
    source: 'Print',
    category: 'INDUSTRY',
    isRead: true,
    links: ['https://guardian.ng/business/banking-sector-resilience'],
    analystName: 'Jane Smith',
    submittedAt: '2024-01-15T11:45:00Z'
  },
  {
    id: '3',
    date: '2024-01-14',
    title: 'Competitive Analysis: Market Share Dynamics',
    content: 'Recent market analysis shows shifting dynamics in the financial services sector, with traditional banks facing increased competition from fintech startups...',
    sentiment: 'Neutral',
    reporter: 'David Wilson',
    publication: 'Financial Times',
    source: 'Online',
    category: 'COMPETITORS',
    isRead: false,
    links: ['https://ft.com/content/market-share-dynamics'],
    analystName: 'Robert Brown',
    submittedAt: '2024-01-14T16:20:00Z'
  },
  {
    id: '4',
    date: '2024-01-14',
    title: 'Regulatory Changes Impact Banking Operations',
    content: 'New regulatory frameworks announced by the Central Bank of Nigeria are expected to significantly impact banking operations across the sector...',
    sentiment: 'Negative',
    reporter: 'Lisa Anderson',
    publication: 'Punch',
    source: 'Print',
    category: 'INDUSTRY',
    isRead: false,
    links: ['https://punchng.com/regulatory-changes-banking'],
    analystName: 'Emily Davis',
    submittedAt: '2024-01-14T14:10:00Z'
  },
  {
    id: '5',
    date: '2024-01-13',
    title: 'Stanbic IBTC Asset Management Launches New Fund',
    content: 'Stanbic IBTC Asset Management has announced the launch of a new investment fund targeting sustainable development goals...',
    sentiment: 'Positive',
    reporter: 'Ahmed Hassan',
    publication: 'Vanguard',
    source: 'Online',
    category: 'SUBSIDIARIES',
    isRead: true,
    links: ['https://vanguardngr.com/stanbic-ibtc-new-fund'],
    analystName: 'John Doe',
    submittedAt: '2024-01-13T10:15:00Z'
  }
];

export function DailyMentionsInboxPage() {
  const [filterValues, setFilterValues] = useState<FilterValues>({});
  const [selectedMention, setSelectedMention] = useState<any>(null);

  // Filter options for daily mentions
  const filterOptions: FilterOption[] = [
    {
      key: 'search',
      label: 'Search',
      type: 'search',
      placeholder: 'Search mentions, titles, content...'
    },
    {
      key: 'date',
      label: 'Date',
      type: 'daterange',
      placeholder: 'Select date range'
    },
    {
      key: 'sentiment',
      label: 'Sentiment',
      type: 'multiselect',
      options: [
        { value: 'Positive', label: 'Positive' },
        { value: 'Neutral', label: 'Neutral' },
        { value: 'Negative', label: 'Negative' }
      ]
    },
    {
      key: 'category',
      label: 'Category',
      type: 'multiselect',
      options: [
        { value: 'SUBSIDIARIES', label: 'Subsidiaries' },
        { value: 'INDUSTRY', label: 'Industry' },
        { value: 'COMPETITORS', label: 'Competitors' },
        { value: 'PHOTO', label: 'Photo' },
        { value: 'ADVERT', label: 'Advertisement' }
      ]
    },
    {
      key: 'source',
      label: 'Source',
      type: 'select',
      options: [
        { value: 'Online', label: 'Online' },
        { value: 'Print', label: 'Print' }
      ]
    },
    {
      key: 'publication',
      label: 'Publication',
      type: 'multiselect',
      options: [
        { value: 'BusinessDay', label: 'BusinessDay' },
        { value: 'The Guardian', label: 'The Guardian' },
        { value: 'Financial Times', label: 'Financial Times' },
        { value: 'Punch', label: 'Punch' },
        { value: 'Vanguard', label: 'Vanguard' }
      ]
    },
    {
      key: 'analyst',
      label: 'Analyst',
      type: 'select',
      options: [
        { value: 'John Doe', label: 'John Doe' },
        { value: 'Jane Smith', label: 'Jane Smith' },
        { value: 'Robert Brown', label: 'Robert Brown' },
        { value: 'Emily Davis', label: 'Emily Davis' }
      ]
    },
    {
      key: 'isRead',
      label: 'Read Status',
      type: 'select',
      options: [
        { value: 'unread', label: 'Unread Only' },
        { value: 'read', label: 'Read Only' }
      ]
    }
  ];

  // Filter the mentions based on filter values
  const filteredMentions = useMemo(() => {
    return mockDailyMentions.filter(mention => {
      // Search filter
      if (filterValues.search) {
        const searchTerm = filterValues.search.toLowerCase();
        const searchableText = `${mention.title} ${mention.content} ${mention.reporter} ${mention.publication}`.toLowerCase();
        if (!searchableText.includes(searchTerm)) return false;
      }

      // Date range filter
      if (filterValues.date && filterValues.date[0] && filterValues.date[1]) {
        const mentionDate = new Date(mention.date);
        const startDate = new Date(filterValues.date[0]);
        const endDate = new Date(filterValues.date[1]);
        if (mentionDate < startDate || mentionDate > endDate) return false;
      }

      // Sentiment filter
      if (filterValues.sentiment && filterValues.sentiment.length > 0) {
        if (!filterValues.sentiment.includes(mention.sentiment)) return false;
      }

      // Category filter
      if (filterValues.category && filterValues.category.length > 0) {
        if (!filterValues.category.includes(mention.category)) return false;
      }

      // Source filter
      if (filterValues.source && mention.source !== filterValues.source) return false;

      // Publication filter
      if (filterValues.publication && filterValues.publication.length > 0) {
        if (!filterValues.publication.includes(mention.publication)) return false;
      }

      // Analyst filter
      if (filterValues.analyst && mention.analystName !== filterValues.analyst) return false;

      // Read status filter
      if (filterValues.isRead) {
        if (filterValues.isRead === 'read' && !mention.isRead) return false;
        if (filterValues.isRead === 'unread' && mention.isRead) return false;
      }

      return true;
    });
  }, [filterValues]);

  const resetFilters = () => {
    setFilterValues({});
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'Positive': return 'bg-green-100 text-green-800';
      case 'Negative': return 'bg-red-100 text-red-800';
      case 'Neutral': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'SUBSIDIARIES': return 'bg-blue-100 text-blue-800';
      case 'INDUSTRY': return 'bg-purple-100 text-purple-800';
      case 'COMPETITORS': return 'bg-orange-100 text-orange-800';
      case 'PHOTO': return 'bg-pink-100 text-pink-800';
      case 'ADVERT': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const unreadCount = filteredMentions.filter(m => !m.isRead).length;
  const totalCount = filteredMentions.length;

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Beautiful Header Section */}
      <div className="bg-gradient-to-r from-indigo-600 via-blue-600 to-cyan-600 rounded-2xl p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute top-0 right-0 w-64 h-64 rounded-full bg-white/10 transform translate-x-32 -translate-y-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 rounded-full bg-white/5 transform -translate-x-24 translate-y-24"></div>
        
        <div className="relative z-10 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 tracking-tight">Daily Mentions Inbox</h1>
            <p className="text-blue-100 text-lg">Review and manage daily media mentions from analysts</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
              <Inbox size={32} className="text-white" />
            </div>
            <div className="text-right">
              <div className="text-sm text-blue-100">Total Mentions</div>
              <div className="text-2xl font-bold text-white">{totalCount}</div>
              <div className="text-sm text-blue-200">{unreadCount} unread</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <UniversalFilter
        filters={filterOptions}
        values={filterValues}
        onChange={setFilterValues}
        onReset={resetFilters}
      />

      {/* Mentions List */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Mentions List */}
        <div className="lg:col-span-2 space-y-4">
          {filteredMentions.length === 0 ? (
            <Card className="border-0 shadow-lg">
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center">
                  <Inbox size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">No mentions found</h3>
                  <p className="text-gray-500">Try adjusting your filters to see more results.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredMentions.map((mention) => (
              <Card 
                key={mention.id} 
                className={`border-0 shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer ${
                  !mention.isRead ? 'bg-blue-50/50 border-l-4 border-l-blue-500' : 'bg-white'
                } ${selectedMention?.id === mention.id ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => setSelectedMention(mention)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {mention.isRead ? (
                        <MailOpen size={16} className="text-gray-400" />
                      ) : (
                        <Mail size={16} className="text-blue-600" />
                      )}
                      <span className="text-sm text-gray-500">
                        {format(new Date(mention.date), 'MMM dd, yyyy')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getSentimentColor(mention.sentiment)}>
                        {mention.sentiment}
                      </Badge>
                      <Badge className={getCategoryColor(mention.category)}>
                        {mention.category}
                      </Badge>
                    </div>
                  </div>
                  
                  <h3 className={`text-lg font-semibold mb-2 ${!mention.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                    {mention.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {mention.content}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <User size={12} />
                        {mention.reporter}
                      </span>
                      <span className="flex items-center gap-1">
                        <Building size={12} />
                        {mention.publication}
                      </span>
                    </div>
                    <span>By {mention.analystName}</span>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Mention Details */}
        <div className="lg:col-span-1">
          {selectedMention ? (
            <Card className="border-0 shadow-lg sticky top-4">
              <CardHeader>
                <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                  <Eye size={20} className="text-indigo-600" />
                  Mention Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">{selectedMention.title}</h3>
                  <p className="text-gray-700 text-sm leading-relaxed">{selectedMention.content}</p>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Date:</span>
                    <span className="text-sm font-medium">{format(new Date(selectedMention.date), 'PPP')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Reporter:</span>
                    <span className="text-sm font-medium">{selectedMention.reporter}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Publication:</span>
                    <span className="text-sm font-medium">{selectedMention.publication}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Source:</span>
                    <span className="text-sm font-medium">{selectedMention.source}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Analyst:</span>
                    <span className="text-sm font-medium">{selectedMention.analystName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Submitted:</span>
                    <span className="text-sm font-medium">{format(new Date(selectedMention.submittedAt), 'PPp')}</span>
                  </div>
                </div>

                {selectedMention.links && selectedMention.links.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Links:</h4>
                    <div className="space-y-1">
                      {selectedMention.links.map((link: string, index: number) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="w-full justify-start text-xs"
                          onClick={() => window.open(link, '_blank')}
                        >
                          <ExternalLink size={12} className="mr-2" />
                          View Article
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button 
                    size="sm" 
                    className="flex-1"
                    onClick={() => {
                      // Mark as read logic here
                      console.log('Mark as read:', selectedMention.id);
                    }}
                  >
                    Mark as Read
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex-1"
                    onClick={() => {
                      // Archive logic here
                      console.log('Archive:', selectedMention.id);
                    }}
                  >
                    Archive
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="border-0 shadow-lg">
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center">
                  <Eye size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Select a mention</h3>
                  <p className="text-gray-500">Click on a mention to view its details.</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
